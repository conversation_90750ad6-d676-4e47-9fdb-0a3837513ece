#include "board.h"
#include "delay.h"
#include "string.h"
/**********************************************************
*** ZDT_X42_V2.0步进闭环控制例程
*** 编写作者：ZHANGDATOU
*** 技术支持：张大头闭环伺服
*** 淘宝店铺：https://zhangdatou.taobao.com
*** CSDN博客：http s://blog.csdn.net/zhangdatou666
*** qq交流群：262438510
**********************************************************/

/**
	* @brief   配置NVIC控制器
	* @param   无
	* @retval  无
	*/
void nvic_init(void)
{	
	// 4bit抢占优先级位
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_4);
	NVIC_InitTypeDef NVIC_InitStructure;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
	NVIC_Init(&NVIC_InitStructure);
	NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
	NVIC_Init(&NVIC_InitStructure);
}

/**
	*	@brief		外设时钟初始化
	*	@param		无
	*	@retval		无
	*/
void clock_init(void)
{
	// 使能GPIOA、AFIO外设时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_AFIO, ENABLE);

	// 使能USART1外设时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);

	// 禁用JTAG
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE);
}

/**
	* @brief   初始化USART
	* @param   无
	* @retval  无
	*/
void usart_init(void)
{
/**********************************************************
***	初始化USART1引脚
**********************************************************/
	// PA9 - USART1_TX
	GPIO_InitTypeDef  GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;				/* 复用推挽输出 */
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// PA10 - USART1_RX
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;					/* 浮空输入 */
	GPIO_Init(GPIOA, &GPIO_InitStructure);

/**********************************************************
***	初始化USART1
**********************************************************/
	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate = 115200;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
	USART_Init(USART1, &USART_InitStructure);

/**********************************************************
***	清除USART1中断
**********************************************************/
	USART1->SR; USART1->DR;
	USART_ClearITPendingBit(USART1, USART_IT_RXNE);

/**********************************************************
***	使能USART1中断
**********************************************************/	
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
	USART_ITConfig(USART1, USART_IT_IDLE, ENABLE);

/**********************************************************
***	使能USART1
**********************************************************/
	USART_Cmd(USART1, ENABLE);
}

#define KEY_RCC (RCC_APB2Periph_GPIOB)
#define KEY_PIN_PORT (GPIOB)
#define KEY_PIN (GPIO_Pin_14)
void vKey_Init(void)
{
	RCC_APB2PeriphClockCmd(KEY_RCC,ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = KEY_PIN;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
	GPIO_Init(KEY_PIN_PORT,&GPIO_InitStructure);
}

bool Key_Getlevel(void)
{
	if(!GPIO_ReadInputDataBit(KEY_PIN_PORT,KEY_PIN))
	{
		delay_ms(10);
		while(!GPIO_ReadInputDataBit(KEY_PIN_PORT,KEY_PIN));
		delay_ms(10);
		return true;
	}
	return false;
}

// LED相关宏定义
#define LED_RCC (RCC_APB2Periph_GPIOC)
#define LED_PIN_PORT (GPIOC)
#define LED_PIN (GPIO_Pin_13)

// LED初始化函数
void vLED_Init(void)
{
	RCC_APB2PeriphClockCmd(LED_RCC, ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;  // 推挽输出
	GPIO_InitStructure.GPIO_Pin = LED_PIN;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
	GPIO_Init(LED_PIN_PORT, &GPIO_InitStructure);

	// LED默认熄灭（PC13通常是低电平点亮，高电平熄灭）
	GPIO_SetBits(LED_PIN_PORT, LED_PIN);
}

// LED闪烁函数
void vLED_Blink(uint8_t times)
{
	for(uint8_t i = 0; i < times; i++)
	{
		GPIO_ResetBits(LED_PIN_PORT, LED_PIN);  // 点亮LED
		delay_ms(200);  // 亮200ms
		GPIO_SetBits(LED_PIN_PORT, LED_PIN);    // 熄灭LED
		delay_ms(200);  // 灭200ms
	}
}

#define USART_PORT GPIOB
#define USART_Hardware USART3
#define USART_TX GPIO_Pin_10
#define USART_RX GPIO_Pin_11
void vUsart_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);
	GPIO_InitTypeDef USART_GPIOStruct;
	USART_GPIOStruct.GPIO_Mode = GPIO_Mode_AF_PP;
	USART_GPIOStruct.GPIO_Pin = USART_TX;
	USART_GPIOStruct.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(USART_PORT,&USART_GPIOStruct);
	USART_GPIOStruct.GPIO_Mode = GPIO_Mode_IPU;
	USART_GPIOStruct.GPIO_Pin = USART_RX;
	USART_GPIOStruct.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(USART_PORT,&USART_GPIOStruct);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3,ENABLE);
	USART_InitTypeDef USART_Struct;
	USART_StructInit(&USART_Struct);
	USART_Struct.USART_BaudRate = 9600;
	USART_Struct.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_Struct.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;
	USART_Struct.USART_Parity = USART_Parity_No;
	USART_Struct.USART_StopBits = USART_StopBits_1;
	USART_Struct.USART_WordLength = USART_WordLength_8b;
	USART_Init(USART_Hardware,&USART_Struct);
	USART_Hardware->CR1 |= 1<<4;
	USART_Hardware->CR1 |= 1<<5;
	USART_Cmd(USART_Hardware,ENABLE);
}
/**
	*	@brief		板载初始化
	*	@param		无
	*	@retval		无
	*/
void board_init(void)
{
	nvic_init();
	clock_init();
	usart_init();
	vUsart_Init();
	vDriverInit();

	vKey_Init();
	vLED_Init();  // 添加LED初始化
}

int fputc(int chr,FILE *p)
{
	USART_SendData(USART1,(uint8_t)chr);  // 改为USART1
	while(USART_GetFlagStatus(USART1,USART_FLAG_TXE)==RESET);
	return chr;
}

#define wait_head (0)
#define wait_Xpoint (1)
#define wait_Ypoint (2)
#define wait_num (3)
#define wait_Tail (4)

#define RECEIVE_SIZE (64)
uint8_t Receive_Finish = 0;
uint8_t Receive_String[RECEIVE_SIZE];//接受数据
uint8_t Receive_Flag = 0;//接受完成标志位
uint8_t Receive_state = 0;  // 确保初始化为0（wait_head状态）
uint8_t XendYstart = 0;
uint8_t Numstart=0;
void USART3_IRQHandler(void)
{
    static uint8_t i = 0;
    static uint32_t rx_count = 0;  // 接收字符计数器

    if(USART_Hardware->SR & (1<<5))
    {
        uint8_t chr;
        chr = USART_Hardware->DR;
        rx_count++;

        // 调试：打印接收到的字符（临时注释减少干扰）
        // printf("RX[%d]: '%c'(0x%02X) state=%d i=%d\r\n", rx_count,
        //        (chr >= 32 && chr <= 126) ? chr : '?', chr, Receive_state, i);

        // 修复：无论当前状态如何，只要接收到冒号且没有正在处理数据，就重新开始
        if(!Receive_Finish && chr == ':')
        {
            printf("=== START NEW PACKET ===\r\n");
            Receive_state = wait_Xpoint;  // 设置为等待X坐标状态
            // 清空接收缓冲区
            for(uint8_t j = 0; j < RECEIVE_SIZE; j++) {
                Receive_String[j] = 0;
            }
            i = 0;
            XendYstart = 0;
            Numstart = 0;
            return;  // 立即返回，避免后续处理
        }
        else if(wait_Xpoint == Receive_state)
        {
            if(chr == ',')
            {
                XendYstart = i;  // 记录X坐标结束位置
                Receive_state++;
                printf("X coord end at pos %d, X='%.*s'\r\n", XendYstart, XendYstart, Receive_String);
            }
            else
            {
                Receive_String[i++] = chr;
                printf("X data: '%c' at pos %d\r\n", chr, i-1);
            }
        }
        else if(wait_Ypoint == Receive_state)
        {
            if(chr == ',')
            {
                Numstart = i;    // 记录Y坐标结束位置，点编号开始位置
                Receive_state++;
                printf("Y coord end at pos %d, Y='%.*s'\r\n", Numstart-XendYstart, Numstart-XendYstart, &Receive_String[XendYstart]);
            }
            else
            {
                Receive_String[i++] = chr;
                printf("Y data: '%c' at pos %d\r\n", chr, i-1);
            }
        }
        else if(wait_num == Receive_state)
        {
            if(chr == '#')  // 坐标结束标志
            {
                Receive_state = wait_head;
                Receive_String[i] = '\0';
                Receive_Finish = 1;
                printf("=== PACKET COMPLETE ===\r\n");
                printf("Full packet: '%s'\r\n", Receive_String);
                i = 0; // 重置索引，为下次接收准备
            }
            else
            {
                Receive_String[i++] = chr;
                printf("Num data: '%c' at pos %d\r\n", chr, i-1);
            }
        }
        else
        {
            printf("Unexpected char in state %d\r\n", Receive_state);
        }
    }

    if(USART_Hardware->SR & (1<<4))
    {
        printf("USART3 IDLE interrupt, state=%d\r\n", Receive_state);
        // 清除IDLE标志
        volatile uint32_t temp = USART_Hardware->SR;
        temp = USART_Hardware->DR;
        (void)temp;  // 避免编译器警告

        // 不要在IDLE中断中修改接收缓冲区，这可能会干扰正常接收
        // Receive_String[i] = '\0';
    }
}
#define POINT_CNT (6)
_DATAPoint Usartdata = {0,};
int16_t usart_point[POINT_CNT][2]={{0,},};
void vData_Get(void)
{
    static int16_t Xpoint = 0, Ypoint = 0, PointNum = 0;
    if(Receive_Finish)
    {
        uint8_t i;

        // 解析X坐标 (从0到XendYstart-1)
        Xpoint = 0;
        for(i = 0; i < XendYstart; i++)
        {
            if(*(Receive_String+i) >= '0' && *(Receive_String+i) <= '9')
                Xpoint = Xpoint*10 + (*(Receive_String+i)-'0');
        }
        // 注意：这里的负号判断逻辑可能不完整，但我们先专注于核心解析错误
        // if(*(Receive_String) == '-') Xpoint = -Xpoint;

        // 【修正1】解析Y坐标，循环应从 XendYstart 开始
        Ypoint = 0;
        for(i = XendYstart; i < Numstart; i++)
        {
            if(*(Receive_String+i) >= '0' && *(Receive_String+i) <= '9')
                Ypoint = Ypoint*10 + (*(Receive_String+i)-'0');
        }
        // if(*(Receive_String+XendYstart) == '-') Ypoint = -Ypoint;

        // 【修正2】解析点编号，应从 Numstart 位置读取
        PointNum = 0; // 先清零
        if(*(Receive_String+Numstart) >= '0' && *(Receive_String+Numstart) <= '9')
        {
            PointNum = *(Receive_String+Numstart) - '0';
        }

        Usartdata.x = Xpoint;
        Usartdata.y = Ypoint;
        Usartdata.num = PointNum;

        printf("Parsed: x=%d, y=%d, num=%d\n", Usartdata.x, Usartdata.y, Usartdata.num); // test
        Receive_Finish = 0; // 处理完毕，允许下一次接收
    }
}

// 重置接收状态的函数
void reset_receive_state(void)
{
	Receive_Finish = 0;
	Receive_state = wait_head;  // 重置为等待头部状态
	XendYstart = 0;
	Numstart = 0;
	printf("接收状态已重置\r\n");
}

// 测试函数：模拟接收完整数据包
void test_simulate_data(void)
{
	printf("=== 模拟测试数据 ===\r\n");

	// 模拟接收 ":100,200,0#"
	const char test_data[] = "100,200,0";

	// 手动设置解析需要的变量
	strcpy((char*)Receive_String, test_data);
	XendYstart = 3;  // "100" 的长度
	Numstart = 7;    // "100,200" 的长度
	Receive_Finish = 1;

	printf("模拟数据: '%s'\r\n", Receive_String);
	printf("XendYstart=%d, Numstart=%d\r\n", XendYstart, Numstart);

	// 调用解析函数
	vData_Get();
}

void _4point_receive(void)
{
	uint8_t j=2;
	static uint8_t i=0;
	uint32_t timeout_count = 0;
	const uint32_t MAX_TIMEOUT = 1000000;  // 超时计数

	printf("开始接收4个坐标点...\r\n");
	reset_receive_state();  // 重置接收状态

	for(i=0;i<POINT_CNT;){usart_point[i][0]=0;usart_point[i++][1]=0;}
	while(j--)
	{
		for(i=0;i<POINT_CNT-2;)
		{
			printf("等待接收点%d...\r\n", i);
			timeout_count = 0;

			do {
				vData_Get();
				timeout_count++;

				// 超时检测
				if(timeout_count > MAX_TIMEOUT)
				{
					printf("接收点%d超时！\r\n", i);
					return;
				}

				// 如果没有接收到数据，定期打印状态
				if(timeout_count % 100000 == 0)
				{
					printf("等待中... Receive_Finish=%d, Usartdata.num=%d\r\n",
						   Receive_Finish, Usartdata.num);
				}

			} while(Usartdata.num != i);

			usart_point[i][0]=Usartdata.x;
			usart_point[i][1]=Usartdata.y;

			printf("成功接收点%d: (%d, %d)\r\n", i, Usartdata.x, Usartdata.y);

			// 每接收到一个点，LED闪烁一次
			vLED_Blink(1);

			i++;
		}
		usart_point[i][0]=usart_point[0][0];
		usart_point[i++][1]=usart_point[0][1];
		usart_point[i][0]=0;
		usart_point[i][1]=0;
	}

	// 接收完所有4个点后，LED快速闪烁3次表示完成
	vLED_Blink(3);

	// 通过USART1打印接收到的四个坐标点
	printf("接收到的四个坐标点:\r\n");
	for(i=0; i<4; i++)
	{
		printf("点%d: (%d, %d)\r\n", i, usart_point[i][0], usart_point[i][1]);
	}
	printf("------------------------\r\n");
}
