#include "board.h"
#include "delay.h"
/**********************************************************
*** ZDT_X42_V2.0步进闭环控制例程
*** 编写作者：ZHANGDATOU
*** 技术支持：张大头闭环伺服
*** 淘宝店铺：https://zhangdatou.taobao.com
*** CSDN博客：http s://blog.csdn.net/zhangdatou666
*** qq交流群：262438510
**********************************************************/

/**
	* @brief   配置NVIC控制器
	* @param   无
	* @retval  无
	*/
void nvic_init(void)
{	
	// 4bit抢占优先级位
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_4);
	NVIC_InitTypeDef NVIC_InitStructure;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
	NVIC_Init(&NVIC_InitStructure);
	NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
	NVIC_Init(&NVIC_InitStructure);
}

/**
	*	@brief		外设时钟初始化
	*	@param		无
	*	@retval		无
	*/
void clock_init(void)
{
	// 使能GPIOA、AFIO外设时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_AFIO, ENABLE);

	// 使能USART1外设时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);

	// 禁用JTAG
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE);
}

/**
	* @brief   初始化USART
	* @param   无
	* @retval  无
	*/
void usart_init(void)
{
/**********************************************************
***	初始化USART1引脚
**********************************************************/
	// PA9 - USART1_TX
	GPIO_InitTypeDef  GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;				/* 复用推挽输出 */
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	// PA10 - USART1_RX
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;					/* 浮空输入 */
	GPIO_Init(GPIOA, &GPIO_InitStructure);

/**********************************************************
***	初始化USART1
**********************************************************/
	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate = 115200;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
	USART_Init(USART1, &USART_InitStructure);

/**********************************************************
***	清除USART1中断
**********************************************************/
	USART1->SR; USART1->DR;
	USART_ClearITPendingBit(USART1, USART_IT_RXNE);

/**********************************************************
***	使能USART1中断
**********************************************************/	
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
	USART_ITConfig(USART1, USART_IT_IDLE, ENABLE);

/**********************************************************
***	使能USART1
**********************************************************/
	USART_Cmd(USART1, ENABLE);
}

#define KEY_RCC (RCC_APB2Periph_GPIOB)
#define KEY_PIN_PORT (GPIOB)
#define KEY_PIN (GPIO_Pin_14)
void vKey_Init(void)
{
	RCC_APB2PeriphClockCmd(KEY_RCC,ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = KEY_PIN;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
	GPIO_Init(KEY_PIN_PORT,&GPIO_InitStructure);
}

bool Key_Getlevel(void)
{
	if(!GPIO_ReadInputDataBit(KEY_PIN_PORT,KEY_PIN))
	{
		delay_ms(10);
		while(!GPIO_ReadInputDataBit(KEY_PIN_PORT,KEY_PIN));
		delay_ms(10);
		return true;
	}
	return false;
}

// LED相关宏定义
#define LED_RCC (RCC_APB2Periph_GPIOC)
#define LED_PIN_PORT (GPIOC)
#define LED_PIN (GPIO_Pin_13)

// LED初始化函数
void vLED_Init(void)
{
	RCC_APB2PeriphClockCmd(LED_RCC, ENABLE);
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;  // 推挽输出
	GPIO_InitStructure.GPIO_Pin = LED_PIN;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
	GPIO_Init(LED_PIN_PORT, &GPIO_InitStructure);

	// LED默认熄灭（PC13通常是低电平点亮，高电平熄灭）
	GPIO_SetBits(LED_PIN_PORT, LED_PIN);
}

// LED闪烁函数
void vLED_Blink(uint8_t times)
{
	for(uint8_t i = 0; i < times; i++)
	{
		GPIO_ResetBits(LED_PIN_PORT, LED_PIN);  // 点亮LED
		delay_ms(200);  // 亮200ms
		GPIO_SetBits(LED_PIN_PORT, LED_PIN);    // 熄灭LED
		delay_ms(200);  // 灭200ms
	}
}

#define USART_PORT GPIOB
#define USART_Hardware USART3
#define USART_TX GPIO_Pin_10
#define USART_RX GPIO_Pin_11
void vUsart_Init(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB,ENABLE);
	GPIO_InitTypeDef USART_GPIOStruct;
	USART_GPIOStruct.GPIO_Mode = GPIO_Mode_AF_PP;
	USART_GPIOStruct.GPIO_Pin = USART_TX;
	USART_GPIOStruct.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(USART_PORT,&USART_GPIOStruct);
	USART_GPIOStruct.GPIO_Mode = GPIO_Mode_IPU;
	USART_GPIOStruct.GPIO_Pin = USART_RX;
	USART_GPIOStruct.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(USART_PORT,&USART_GPIOStruct);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3,ENABLE);
	USART_InitTypeDef USART_Struct;
	USART_StructInit(&USART_Struct);
	USART_Struct.USART_BaudRate = 115200;
	USART_Struct.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_Struct.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;
	USART_Struct.USART_Parity = USART_Parity_No;
	USART_Struct.USART_StopBits = USART_StopBits_1;
	USART_Struct.USART_WordLength = USART_WordLength_8b;
	USART_Init(USART_Hardware,&USART_Struct);
	USART_Hardware->CR1 |= 1<<4;
	USART_Hardware->CR1 |= 1<<5;
	USART_Cmd(USART_Hardware,ENABLE);
}
/**
	*	@brief		板载初始化
	*	@param		无
	*	@retval		无
	*/
void board_init(void)
{
	nvic_init();
	clock_init();
	usart_init();
	vUsart_Init();
	vDriverInit();

	vKey_Init();
	vLED_Init();  // 添加LED初始化
}

int fputc(int chr,FILE *p)
{
	USART_SendData(USART1,(uint8_t)chr);  // 改为USART1
	while(USART_GetFlagStatus(USART1,USART_FLAG_TXE)==RESET);
	return chr;
}

#define wait_head (0)
#define wait_Xpoint (1)
#define wait_Ypoint (2)
#define wait_num (3)
#define wait_Tail (4)

#define RECEIVE_SIZE (64)
uint8_t Receive_Finish = 0;
uint8_t Receive_String[RECEIVE_SIZE];//接受数据
uint8_t Receive_Flag = 0;//接受完成标志位
uint8_t Receive_state = 0;
uint8_t XendYstart = 0;
uint8_t Numstart=0;
void USART3_IRQHandler(void)
{
    static uint8_t i = 0;
    if(USART_Hardware->SR & (1<<5))
    {
        uint8_t chr;
        chr = USART_Hardware->DR;
        
        if(!Receive_Finish && wait_head == Receive_state && chr == ':')
        {
            Receive_state++;
            while(i)
                Receive_String[--i] = '\0';
            i = 0;
        }
        else if(wait_Xpoint == Receive_state)
        {
            if(chr == ',')
            {
                XendYstart = i;  // 记录X坐标结束位置
                Receive_state++;
            }
            else
            {
                Receive_String[i++] = chr;
            }
        }
        else if(wait_Ypoint == Receive_state)
        {
            if(chr == ',')
            {
                Numstart = i;    // 记录Y坐标结束位置，点编号开始位置
                Receive_state++;
            }
            else 
            {
                Receive_String[i++] = chr;
            }
        }
        else if(wait_num == Receive_state)
        {
            if(chr == '#')  // 坐标结束标志
            {
                Receive_state = wait_head;
                Receive_String[i] = '\0';
                Receive_Finish = 1;
                // i = 0; // 可选：重置索引，为下次接收准备
            }
            else 
            {
                Receive_String[i++] = chr;
            }
        }
    }
    
    if(USART_Hardware->SR & (1<<4))
    {
        USART_Hardware->SR;
        USART_Hardware->DR;
        Receive_String[i] = '\0';
    }
}
#define POINT_CNT (6)
_DATAPoint Usartdata = {0,};
int16_t usart_point[POINT_CNT][2]={{0,},};
void vData_Get(void)
{
    static int16_t Xpoint = 0, Ypoint = 0, PointNum = 0;
    if(Receive_Finish)
    {
        uint8_t i;

        // 解析X坐标 (从0到XendYstart-1)
        Xpoint = 0;
        for(i = 0; i < XendYstart; i++)
        {
            if(*(Receive_String+i) >= '0' && *(Receive_String+i) <= '9')
                Xpoint = Xpoint*10 + (*(Receive_String+i)-'0');
        }
        // 注意：这里的负号判断逻辑可能不完整，但我们先专注于核心解析错误
        // if(*(Receive_String) == '-') Xpoint = -Xpoint;

        // 【修正1】解析Y坐标，循环应从 XendYstart 开始
        Ypoint = 0;
        for(i = XendYstart; i < Numstart; i++)
        {
            if(*(Receive_String+i) >= '0' && *(Receive_String+i) <= '9')
                Ypoint = Ypoint*10 + (*(Receive_String+i)-'0');
        }
        // if(*(Receive_String+XendYstart) == '-') Ypoint = -Ypoint;

        // 【修正2】解析点编号，应从 Numstart 位置读取
        PointNum = 0; // 先清零
        if(*(Receive_String+Numstart) >= '0' && *(Receive_String+Numstart) <= '9')
        {
            PointNum = *(Receive_String+Numstart) - '0';
        }

        Usartdata.x = Xpoint;
        Usartdata.y = Ypoint;
        Usartdata.num = PointNum;

        printf("Parsed: x=%d, y=%d, num=%d\n", Usartdata.x, Usartdata.y, Usartdata.num); // test
        Receive_Finish = 0; // 处理完毕，允许下一次接收
    }
}

void _4point_receive(void)
{
	uint8_t j=2;
	static uint8_t i=0;

	for(i=0;i<POINT_CNT;){usart_point[i][0]=0;usart_point[i++][1]=0;}
	while(j--)
	{
			for(i=0;i<POINT_CNT-2;)
		{
			vData_Get();
			if(Usartdata.num==i)
			{
				usart_point[i][0]=Usartdata.x;
				usart_point[i][1]=Usartdata.y;

				// 每接收到一个点，LED闪烁一次
				vLED_Blink(1);

				i++;
			}
		}
				usart_point[i][0]=usart_point[0][0];
				usart_point[i++][1]=usart_point[0][1];
				usart_point[i][0]=0;
				usart_point[i][1]=0;
	}

	// 接收完所有4个点后，LED快速闪烁3次表示完成
	vLED_Blink(3);

	// 通过USART1打印接收到的四个坐标点
	printf("接收到的四个坐标点:\r\n");
	for(i=0; i<4; i++)
	{
		printf("点%d: (%d, %d)\r\n", i, usart_point[i][0], usart_point[i][1]);
	}
	printf("------------------------\r\n");
}
