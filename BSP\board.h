#ifndef __BOARD_H
#define __BOARD_H

#include "stm32f10x.h"
#include "stdio.h"
#include "motor.h"

void nvic_init(void);
void clock_init(void);
void usart_init(void);
void board_init(void);
bool Key_Getlevel(void);

void vData_Get(void);//����ת��
void _4point_receive(void);//�ɼ�4��

// LED相关函数声明
void vLED_Init(void);
void vLED_Blink(uint8_t times);

typedef struct _DATAPoint_//�����ݽṹ������
{
	int16_t x;//����
	int16_t y;
	int32_t x_step;
	uint8_t x_max_flag;
	int32_t y_step;
	uint8_t y_max_flag;
	float s;//�ٶȱ�ֵy speed/x speed
	uint8_t mode;//����ģʽ
	uint8_t num;
}_DATAPoint;

extern int16_t usart_point[6][2];
extern _DATAPoint Usartdata;

#endif
